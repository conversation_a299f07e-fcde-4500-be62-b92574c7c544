<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateBarcodeProductsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('barcode_products', function (Blueprint $table) {
            $table->id();
            $table->string('Name')->nullable();
            $table->string('Code')->nullable();
            $table->integer('Qty')->nullable();
            $table->decimal('Price', 15, 2)->nullable();
            $table->integer('Group')->nullable();
            $table->unsignedBigInteger('Product')->nullable();
            $table->unsignedBigInteger('V1')->nullable();
            $table->unsignedBigInteger('V2')->nullable();
            $table->unsignedBigInteger('Unit')->nullable();
            $table->timestamps();

            $table->foreign('Product')->references('id')->on('products')->onDelete('set null');
            // $table->foreign('V1')->references('id')->on('sub_virables')->onDelete('set null');
            // $table->foreign('V2')->references('id')->on('sub_virables')->onDelete('set null');
            // Temporarily commented out - will be added after measuerments table is created
            // $table->foreign('Unit')->references('id')->on('measuerments')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('barcode_products');
    }
}
