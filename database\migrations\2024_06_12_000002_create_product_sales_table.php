<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateProductSalesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('product_sales', function (Blueprint $table) {
            $table->id();
            $table->string('Product_Code')->nullable();
            $table->string('P_Ar_Name')->nullable();
            $table->string('P_En_Name')->nullable();
            $table->string('V_Name')->nullable();
            $table->string('VV_Name')->nullable();
            $table->float('Original_Qty')->nullable();
            $table->float('AvQty')->nullable();
            $table->float('Qty')->nullable();
            $table->float('SmallQty')->nullable();
            $table->string('SmallCode')->nullable();
            $table->float('Price')->nullable();
            $table->float('CostPrice')->nullable();
            $table->float('Discount')->nullable();
            $table->float('TDiscount')->nullable();
            $table->unsignedBigInteger('Tax')->nullable();
            $table->float('Total_Bf_Tax')->nullable();
            $table->float('Total_Tax')->nullable();
            $table->float('Total')->nullable();
            $table->unsignedBigInteger('Store')->nullable();
            $table->date('Date')->nullable();
            $table->unsignedBigInteger('Product')->nullable();
            $table->date('Exp_Date')->nullable();
            $table->unsignedBigInteger('V1')->nullable();
            $table->unsignedBigInteger('V2')->nullable();
            $table->unsignedBigInteger('Unit')->nullable();
            $table->unsignedBigInteger('Sales')->nullable();
            $table->string('Code')->nullable();
            $table->string('Refernce_Number')->nullable();
            $table->unsignedBigInteger('Safe')->nullable();
            $table->unsignedBigInteger('Client')->nullable();
            $table->unsignedBigInteger('Executor')->nullable();
            $table->unsignedBigInteger('Delegate')->nullable();
            $table->unsignedBigInteger('Coin')->nullable();
            $table->unsignedBigInteger('User')->nullable();
            $table->unsignedBigInteger('Cost_Center')->nullable();
            $table->string('Type')->nullable();
            $table->unsignedBigInteger('Ship')->nullable();
            $table->string('Patch_Number')->nullable();
            $table->unsignedBigInteger('Branch')->nullable();
            $table->unsignedBigInteger('Group')->nullable();
            $table->unsignedBigInteger('Brand')->nullable();
            $table->unsignedBigInteger('CustomerGroup')->nullable();
            $table->string('Status')->nullable();
            $table->string('Payment_Method')->nullable();
            $table->string('CoinCode')->nullable();
            $table->float('CoinRate')->nullable();
            $table->float('CoinPrice')->nullable();
            $table->float('AmountEGP')->nullable();
            $table->float('SalesTotal')->nullable();
            $table->float('DiscountAmount')->nullable();
            $table->float('NetTotal')->nullable();
            $table->string('TaxType')->nullable();
            $table->float('TaxAmount')->nullable();
            $table->string('TaxSubType')->nullable();
            $table->float('TaxRate')->nullable();
            $table->float('TotalBill')->nullable();
            $table->string('Pro_Note')->nullable();
            $table->unsignedBigInteger('SubVID')->nullable();
            $table->float('Total_Wight')->nullable();
            $table->float('UnitRate')->nullable();
            $table->float('weight')->nullable();
            $table->string('SalesProDesc')->nullable();
            $table->timestamps();

            // Temporarily commented out - will be added after referenced tables are created
            // $table->foreign('Tax')->references('id')->on('taxes')->onDelete('set null');
            // $table->foreign('Store')->references('id')->on('stores')->onDelete('set null');
            $table->foreign('Product')->references('id')->on('products')->onDelete('set null');
            // $table->foreign('V1')->references('id')->on('sub_virables')->onDelete('set null');
            // $table->foreign('V2')->references('id')->on('sub_virables')->onDelete('set null');
            // $table->foreign('Unit')->references('id')->on('measuerments')->onDelete('set null');
            // $table->foreign('Sales')->references('id')->on('sales')->onDelete('set null');
            // $table->foreign('Safe')->references('id')->on('acccounting_manuals')->onDelete('set null');
            // $table->foreign('Client')->references('id')->on('acccounting_manuals')->onDelete('set null');
            // $table->foreign('Executor')->references('id')->on('employess')->onDelete('set null');
            // $table->foreign('Delegate')->references('id')->on('employess')->onDelete('set null');
            // $table->foreign('Coin')->references('id')->on('coins')->onDelete('set null');
            // $table->foreign('User')->references('id')->on('admins')->onDelete('set null');
            // $table->foreign('Cost_Center')->references('id')->on('cost_centers')->onDelete('set null');
            // $table->foreign('Ship')->references('id')->on('acccounting_manuals')->onDelete('set null');
            // $table->foreign('Branch')->references('id')->on('branches')->onDelete('set null');
            // $table->foreign('Group')->references('id')->on('items_groups')->onDelete('set null');
            // $table->foreign('Brand')->references('id')->on('brands')->onDelete('set null');
            // $table->foreign('CustomerGroup')->references('id')->on('customers_groups')->onDelete('set null');
            // $table->foreign('SubVID')->references('id')->on('sub_virables')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('product_sales');
    }
}
