<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateProductsQtiesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('products_qties', function (Blueprint $table) {
            $table->id();
            $table->float('Qty')->nullable();
            $table->string('P_Ar_Name')->nullable();
            $table->string('P_En_Name')->nullable();
            $table->string('P_Code')->nullable();
            $table->string('PP_Code')->nullable();
            $table->string('PPP_Code')->nullable();
            $table->string('PPPP_Code')->nullable();
            $table->string('V_Name')->nullable();
            $table->string('VV_Name')->nullable();
            $table->float('Price')->nullable();
            $table->float('TotalCost')->nullable();
            $table->unsignedBigInteger('Pro_Stores')->nullable();
            $table->unsignedBigInteger('Store')->nullable();
            $table->unsignedBigInteger('Unit')->nullable();
            $table->unsignedBigInteger('Product')->nullable();
            $table->string('Original')->nullable();
            $table->unsignedBigInteger('V1')->nullable();
            $table->unsignedBigInteger('V2')->nullable();
            $table->unsignedBigInteger('Low_Unit')->nullable();
            $table->string('Patch_Number')->nullable();
            $table->date('Exp_Date')->nullable();
            $table->float('Price_Sale')->nullable();
            $table->string('SearchCode1')->nullable();
            $table->string('SearchCode2')->nullable();
            $table->unsignedBigInteger('Group')->nullable();
            $table->unsignedBigInteger('Brand')->nullable();
            $table->unsignedBigInteger('Branch')->nullable();
            $table->timestamps();

            // $table->foreign('Pro_Stores')->references('id')->on('products_stores')->onDelete('set null');
            // Temporarily commented out - will be added after referenced tables are created
            // $table->foreign('Store')->references('id')->on('stores')->onDelete('set null');
            // $table->foreign('Unit')->references('id')->on('measuerments')->onDelete('set null');
            $table->foreign('Product')->references('id')->on('products')->onDelete('set null');
            // $table->foreign('V1')->references('id')->on('sub_virables')->onDelete('set null');
            // $table->foreign('V2')->references('id')->on('sub_virables')->onDelete('set null');
            // $table->foreign('Low_Unit')->references('id')->on('measuerments')->onDelete('set null');
            // $table->foreign('Group')->references('id')->on('items_groups')->onDelete('set null');
            // $table->foreign('Brand')->references('id')->on('brands')->onDelete('set null');
            // $table->foreign('Branch')->references('id')->on('branches')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('products_qties');
    }
}
