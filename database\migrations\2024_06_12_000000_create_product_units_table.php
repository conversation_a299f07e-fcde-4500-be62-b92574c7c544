<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateProductUnitsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('product_units', function (Blueprint $table) {
            $table->id();
            $table->float('Rate')->nullable();
            $table->string('Barcode')->nullable();
            $table->float('Price')->nullable();
            $table->float('Price_Two')->nullable();
            $table->float('Price_Three')->nullable();
            $table->string('P_Ar_Name')->nullable();
            $table->string('P_En_Name')->nullable();
            $table->string('P_Type')->nullable();
            $table->unsignedBigInteger('Unit')->nullable();
            $table->unsignedBigInteger('Product')->nullable();
            $table->boolean('Def')->default(false);
            $table->unsignedBigInteger('Brand')->nullable();
            $table->unsignedBigInteger('Group')->nullable();
            $table->timestamps();

            // Temporarily commented out - will be added after referenced tables are created
            // $table->foreign('Unit')->references('id')->on('measuerments')->onDelete('set null');
            $table->foreign('Product')->references('id')->on('products')->onDelete('set null');
            // $table->foreign('Brand')->references('id')->on('brands')->onDelete('set null');
            // $table->foreign('Group')->references('id')->on('items_groups')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('product_units');
    }
}
